# 仿真配置系统使用指南

## 概述

新的配置系统将所有仿真相关的参数集中到 `simulation_config.py` 文件中，方便调试和测试。

## 配置模板

系统提供了4个预定义的配置模板：

### 1. default - 默认配置
- 标准的仿真参数
- 实时可视化模式
- 适合一般测试

### 2. fast - 快速测试配置
- 快速可视化模式（无暂停）
- 高速度倍数（50x）
- 较短的仿真时间（600秒）
- 适合快速验证

### 3. debug - 调试配置
- 逐步可视化模式
- 启用详细调试输出
- 保存中间状态
- 适合算法调试

### 4. production - 生产配置
- 无可视化模式
- 保存详细日志
- 适合批量实验

## 使用方法

### 1. 使用配置模板

```bash
# 使用默认配置
python run_simulation.py

# 使用快速测试配置
python run_simulation.py --config-template fast

# 使用调试配置
python run_simulation.py --config-template debug

# 使用生产配置
python run_simulation.py --config-template production
```

### 2. 使用自定义配置文件

```bash
# 从配置文件加载
python run_simulation.py --config-file example_configs/fast_test_config.json

# 保存当前配置到文件
python run_simulation.py --config-template fast --save-config my_config.json
```

### 3. 命令行参数覆盖

```bash
# 覆盖特定参数
python run_simulation.py --config-template fast --case 2 --max-time 300 --visualization real-time

# 覆盖算法选择
python run_simulation.py --task-algorithm hungarian --path-algorithm dijkstra --reschedule-algorithm replan
```

## 配置参数说明

### 基本仿真参数
- `CASE_NUMBER`: 实验case编号
- `SIMULATION_TIME_STEP`: 仿真时间步长（秒）
- `MAX_SIMULATION_TIME`: 最大仿真时间（秒）

### 可视化参数
- `VISUALIZATION_MODE`: 可视化模式
  - `real-time`: 实时可视化
  - `step`: 逐步可视化
  - `fast`: 快速模式（无暂停）
  - `final`: 仅显示最终结果
  - `none`: 无可视化
- `VISUALIZATION_SPEED`: 可视化速度倍数

### 算法配置
- `TASK_ALLOCATION_CONFIG`: 任务分配算法配置
- `PATH_PLANNING_CONFIG`: 路径规划算法配置
- `TASK_RESCHEDULING_CONFIG`: 任务重调度算法配置

### 调试配置
- `DEBUG_CONFIG`: 调试相关设置
- `PERFORMANCE_CONFIG`: 性能指标配置

## 示例

### 快速测试
```bash
python run_simulation.py --config-template fast --case 1
```

### 调试特定算法
```bash
python run_simulation.py --config-template debug --task-algorithm hungarian --visualization step
```

### 批量实验
```bash
python run_simulation.py --config-template production --case 1 --max-time 1800
```

### 自定义配置
```bash
python run_simulation.py --config-file my_custom_config.json --case 3
```

## 配置文件格式

配置文件使用JSON格式，包含所有配置参数。参考 `example_configs/fast_test_config.json` 文件。

## 编程接口

在代码中也可以直接使用配置系统：

```python
from simulation_config import get_config, create_custom_config
from simulation import Simulation

# 使用预定义模板
config = get_config('fast')
simulation = Simulation(sim_config=config)

# 创建自定义配置
config = create_custom_config(
    case_number=2,
    visualization_mode='fast',
    max_simulation_time=600
)
simulation = Simulation(sim_config=config)
```

## 注意事项

1. 命令行参数会覆盖配置文件中的对应参数
2. 配置模板的优先级：命令行参数 > 配置文件 > 配置模板
3. 保存的配置文件包含所有参数，可以作为完整的配置备份
4. 调试模式会产生大量输出，建议仅在需要时使用
