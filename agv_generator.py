"""
AGV生成器
"""
import random
from agv import AGV

class AGVGenerator:
    def __init__(self, agv_params, task_points):
        """
        初始化AGV生成器

        Args:
            agv_params: AGV参数配置
            task_points: 任务点配置
        """
        # AGV参数配置
        self.params = agv_params
        # 任务点配置
        self.task_points = task_points
        # AGV列表
        self.agv_list = []
        # 总AGV数量
        self.total_agvs = self.params.get('total_agvs', 5)
        # AGV类型配置
        self.agv_types = self.params.get('agv_types', {})
        # 共同参数
        self.common_params = self.params.get('common', {})
        # 随机数生成器
        self.random = random.Random()

    def generate_agvs(self):
        """根据配置生成AGV列表"""
        # 清空AGV列表
        self.agv_list = []

        # 计算每种AGV类型的数量
        agv_counts = {}
        remaining_agvs = self.total_agvs

        # 先分配有比例的AGV类型
        for agv_type, config in self.agv_types.items():
            ratio = config.get('ratio', 0)
            count = int(self.total_agvs * ratio)
            agv_counts[agv_type] = count
            remaining_agvs -= count

        # 如果还有剩余AGV，分配给第一个AGV类型
        if remaining_agvs > 0 and self.agv_types:
            first_type = list(self.agv_types.keys())[0]
            agv_counts[first_type] += remaining_agvs

        # 生成每种类型的AGV
        agv_id = 0
        for agv_type, count in agv_counts.items():
            if agv_type in self.agv_types and count > 0:
                type_agvs = self._generate_agvs_by_type(agv_type, count, agv_id)
                self.agv_list.extend(type_agvs)
                agv_id += count

        return self.agv_list

    def _generate_agvs_by_type(self, agv_type, count, start_id):
        """根据AGV类型生成指定数量的AGV

        Args:
            agv_type: AGV类型
            count: AGV数量
            start_id: 起始ID

        Returns:
            list: AGV对象列表
        """
        if agv_type not in self.agv_types:
            return []

        config = self.agv_types[agv_type]
        agvs = []

        # 获取初始位置配置
        position_config = config.get('initial_positions', {})
        mode = position_config.get('mode', 'fixed')

        if mode == 'fixed':
            # 固定点位
            positions = position_config.get('positions', [])
            if not positions:
                # 如果没有指定点位，使用所有任务点
                positions = list(self.task_points.keys())
        else:
            # 随机点位
            candidates = position_config.get('candidates', [])
            if not candidates:
                # 如果没有指定候选点位，使用所有任务点
                candidates = list(self.task_points.keys())
            positions = candidates

        # 生成AGV
        for i in range(count):
            agv_id = f"agv_{start_id + i}"

            # 选择初始位置
            if mode == 'fixed' and i < len(positions):
                # 固定点位模式，按顺序选择
                position = positions[i]
            else:
                # 随机选择点位
                position = self.random.choice(positions)

            # 检查点位是否有效
            if position not in self.task_points:
                # 如果点位无效，使用默认点位
                position = list(self.task_points.keys())[0] if self.task_points else 'P1'

            # 创建AGV
            # 获取AGV速度
            speed_config = config.get('speed', {})
            speed = speed_config.get('normal', 1.0) if isinstance(speed_config, dict) else speed_config or 1.0

            agv = AGV(
                agv_id=agv_id,
                agv_type=agv_type,
                position=position,  # 使用任务点ID作为位置
                speed=speed
            )

            agvs.append(agv)

        return agvs

    def save_agv_list(self, file_path):
        """将AGV列表存储到文件

        Args:
            file_path: 存储文件路径
        """
        # 获取AGV数据
        agv_data = [agv.get_info() for agv in self.agv_list]

        # 使用全局配置中的保存函数
        import config
        config.save_data_to_file(agv_data, file_path)
