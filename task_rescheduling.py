"""
任务重调度算法接口
负责在AGV路径因动态障碍导致当前路径不可行时，重新规划路径或重新分配任务
"""

class TaskRescheduler:
    def __init__(self, algorithm="replan"):
        """
        初始化任务重调度器
        
        Args:
            algorithm: 任务重调度算法，可选值：
                - "replan": 重新规划路径
                - "reassign": 重新分配任务
                - "hybrid": 混合策略
                - "custom": 自定义算法
        """
        self.algorithm = algorithm
    
    def reschedule(self, agv, task_pool, agv_list, obstacle_pool, path_planner, current_time):
        """
        重新调度任务
        
        Args:
            agv: 需要重调度的AGV
            task_pool: 任务池
            agv_list: AGV列表
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            bool: 是否成功重调度
        """
        if self.algorithm == "replan":
            return self._replan_path(agv, obstacle_pool, path_planner, current_time)
        elif self.algorithm == "reassign":
            return self._reassign_task(agv, task_pool, agv_list, obstacle_pool, path_planner, current_time)
        elif self.algorithm == "hybrid":
            return self._hybrid_reschedule(agv, task_pool, agv_list, obstacle_pool, path_planner, current_time)
        elif self.algorithm == "custom":
            return self._custom_reschedule(agv, task_pool, agv_list, obstacle_pool, path_planner, current_time)
        else:
            # 默认使用重新规划路径
            return self._replan_path(agv, obstacle_pool, path_planner, current_time)
    
    def _replan_path(self, agv, obstacle_pool, path_planner, current_time):
        """
        重新规划路径
        
        Args:
            agv: 需要重调度的AGV
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            bool: 是否成功重规划路径
        """
        if not agv.current_task:
            return False
        
        # 确定起点和终点
        start = agv.position
        
        # 如果AGV还没有到达任务起点，终点为任务起点
        if agv.target_position == agv.current_task.start_point:
            end = agv.current_task.start_point
        else:
            # 否则终点为任务终点
            end = agv.current_task.end_point
        
        # 重新规划路径
        new_path = path_planner.plan_path(
            start=start,
            end=end,
            obstacle_pool=obstacle_pool,
            current_time=current_time
        )
        
        # 如果无法规划新路径，返回失败
        if not new_path:
            return False
        
        # 更新AGV路径
        agv.path = new_path
        agv.path_index = 0
        
        return True
    
    def _reassign_task(self, agv, task_pool, agv_list, obstacle_pool, path_planner, current_time):
        """
        重新分配任务
        
        Args:
            agv: 需要重调度的AGV
            task_pool: 任务池
            agv_list: AGV列表
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            bool: 是否成功重分配任务
        """
        if not agv.current_task:
            return False
        
        # 将当前任务重新加入任务池
        task = agv.current_task
        task_pool.append(task)
        
        # 重置AGV状态
        agv.current_task = None
        agv.status = 'idle'
        agv.path = []
        agv.path_index = 0
        agv.target_position = None
        
        # 尝试为其他AGV分配这个任务
        idle_agvs = [a for a in agv_list if a.status == 'idle' and a.id != agv.id]
        
        if not idle_agvs:
            return False
        
        # 计算每个AGV到任务起点的距离
        agv_distances = []
        for idle_agv in idle_agvs:
            # 计算AGV到任务起点的路径
            path = path_planner.plan_path(
                start=idle_agv.position,
                end=task.start_point,
                obstacle_pool=obstacle_pool,
                current_time=current_time
            )
            
            # 如果无法规划路径，距离设为无穷大
            if not path:
                distance = float('inf')
            else:
                distance = path_planner.calculate_path_length(path)
            
            agv_distances.append((idle_agv, distance))
        
        # 选择距离最短的AGV
        agv_distances.sort(key=lambda x: x[1])
        
        if agv_distances and agv_distances[0][1] < float('inf'):
            best_agv = agv_distances[0][0]
            
            # 分配任务给新的AGV
            best_agv.assign_task(task)
            
            # 规划路径
            path = path_planner.plan_path(
                start=best_agv.position,
                end=task.start_point,
                obstacle_pool=obstacle_pool,
                current_time=current_time
            )
            
            if not path:
                # 如果无法规划路径，取消分配
                best_agv.current_task = None
                best_agv.status = 'idle'
                return False
            
            best_agv.path = path
            best_agv.path_index = 0
            
            # 从任务池中移除任务
            task_pool.remove(task)
            
            return True
        
        return False
    
    def _hybrid_reschedule(self, agv, task_pool, agv_list, obstacle_pool, path_planner, current_time):
        """
        混合策略重调度
        先尝试重新规划路径，如果失败则尝试重新分配任务
        
        Args:
            agv: 需要重调度的AGV
            task_pool: 任务池
            agv_list: AGV列表
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            bool: 是否成功重调度
        """
        # 先尝试重新规划路径
        if self._replan_path(agv, obstacle_pool, path_planner, current_time):
            return True
        
        # 如果重新规划路径失败，尝试重新分配任务
        return self._reassign_task(agv, task_pool, agv_list, obstacle_pool, path_planner, current_time)
    
    def _custom_reschedule(self, agv, task_pool, agv_list, obstacle_pool, path_planner, current_time):
        """
        自定义重调度算法
        
        Args:
            agv: 需要重调度的AGV
            task_pool: 任务池
            agv_list: AGV列表
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            bool: 是否成功重调度
        """
        # TODO: 实现自定义重调度算法
        # 这里简单地调用混合策略作为占位符
        return self._hybrid_reschedule(agv, task_pool, agv_list, obstacle_pool, path_planner, current_time)
