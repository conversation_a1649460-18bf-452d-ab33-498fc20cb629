{"case_number": 1, "simulation_time_step": 0.1, "max_simulation_time": 600, "visualization_mode": "fast", "visualization_speed": 50.0, "task_file_path": "output/tasks/tasks_case1_test.json", "obstacle_file_path": "output/obstacles/obstacles_case1_test.json", "agv_file_path": "output/tasks/tasks_case1_agvs_test.json", "output_dir": "output", "agv_config": {"default_count": 5, "default_speed": 1.0, "speed_variance": 0.2, "initial_positions": ["P1", "P5", "P16", "P6", "P6"], "agv_types": {"standard_agv": {"speed": 1.0, "capacity": 1}, "fast_agv": {"speed": 1.5, "capacity": 1}, "heavy_agv": {"speed": 0.8, "capacity": 2}}}, "task_allocation_config": {"algorithm": "greedy", "allocation_interval": 1.0, "priority_weights": {"distance": 0.6, "urgency": 0.3, "agv_efficiency": 0.1}}, "path_planning_config": {"algorithm": "astar", "heuristic_weight": 1.0, "obstacle_buffer": 0.5, "path_smoothing": true, "dynamic_replanning": true}, "task_rescheduling_config": {"algorithm": "hybrid", "replan_threshold": 2.0, "max_replan_attempts": 3, "reassign_penalty": 10.0}, "performance_config": {"metrics_update_interval": 10.0, "save_detailed_logs": false, "log_level": "INFO", "export_format": "json"}, "debug_config": {"enable_debug_output": false, "debug_agv_paths": false, "debug_task_allocation": false, "debug_collision_detection": false, "save_intermediate_states": false, "state_save_interval": 60.0}, "experiment_config": {"run_multiple_cases": false, "case_range": [1, 5], "repeat_count": 1, "random_seed": 42, "parallel_execution": false}, "environment_config": {"workspace_bounds": {"x_min": -2, "x_max": 22, "y_min": -2, "y_max": 22}, "grid_resolution": 0.1, "collision_detection_enabled": true, "deadlock_detection_enabled": true, "deadlock_timeout": 30.0}}