"""
路径规划算法接口
负责为AGV规划从起点到终点的路径
"""
import math
import heapq

class PathPlanner:
    def __init__(self, task_points, channels, algorithm="astar"):
        """
        初始化路径规划器
        
        Args:
            task_points: 任务点字典，键为点ID，值为坐标
            channels: 通道字典，键为(起点ID, 终点ID)，值为通道信息
            algorithm: 路径规划算法，可选值：
                - "astar": A*算法
                - "dijkstra": Dijkstra算法
                - "rrt": 快速随机树算法
                - "custom": 自定义算法
        """
        self.task_points = task_points
        self.channels = channels
        self.algorithm = algorithm
        
        # 构建图结构
        self.graph = self._build_graph()
    
    def _build_graph(self):
        """
        构建图结构
        
        Returns:
            dict: 图结构，键为点ID，值为相邻点及距离的字典
        """
        graph = {point_id: {} for point_id in self.task_points}
        
        for (start, end), channel_info in self.channels.items():
            distance = channel_info.get('distance', 1)
            
            # 添加双向边
            if start in graph:
                graph[start][end] = distance
            if end in graph:
                graph[end][start] = distance
        
        return graph
    
    def plan_path(self, start, end, obstacle_pool=None, current_time=None):
        """
        规划从起点到终点的路径
        
        Args:
            start: 起点ID或坐标
            end: 终点ID或坐标
            obstacle_pool: 障碍物池
            current_time: 当前时间
            
        Returns:
            list: 路径点列表，每个元素为点ID
        """
        # 如果起点或终点是坐标，转换为最近的点ID
        if not isinstance(start, str):
            start = self._find_nearest_point(start)
        if not isinstance(end, str):
            end = self._find_nearest_point(end)
        
        if self.algorithm == "astar":
            return self._astar(start, end, obstacle_pool, current_time)
        elif self.algorithm == "dijkstra":
            return self._dijkstra(start, end, obstacle_pool, current_time)
        elif self.algorithm == "rrt":
            return self._rrt(start, end, obstacle_pool, current_time)
        elif self.algorithm == "custom":
            return self._custom_path_planning(start, end, obstacle_pool, current_time)
        else:
            # 默认使用A*算法
            return self._astar(start, end, obstacle_pool, current_time)
    
    def _find_nearest_point(self, position):
        """
        找到距离给定坐标最近的任务点
        
        Args:
            position: 坐标 (x, y)
            
        Returns:
            str: 最近任务点的ID
        """
        min_distance = float('inf')
        nearest_point = None
        
        for point_id, point_info in self.task_points.items():
            x, y = point_info['x'], point_info['y']
            distance = math.sqrt((position[0] - x) ** 2 + (position[1] - y) ** 2)
            
            if distance < min_distance:
                min_distance = distance
                nearest_point = point_id
        
        return nearest_point
    
    def _astar(self, start, end, obstacle_pool=None, current_time=None):
        """
        A*算法规划路径
        
        Args:
            start: 起点ID
            end: 终点ID
            obstacle_pool: 障碍物池
            current_time: 当前时间
            
        Returns:
            list: 路径点列表，每个元素为点ID
        """
        if start == end:
            return [start]
        
        # 开放列表和关闭列表
        open_list = []
        closed_set = set()
        
        # 记录每个节点的父节点
        parent = {}
        
        # 记录从起点到每个节点的实际距离
        g_score = {point_id: float('inf') for point_id in self.task_points}
        g_score[start] = 0
        
        # 记录从起点经过每个节点到终点的估计距离
        f_score = {point_id: float('inf') for point_id in self.task_points}
        f_score[start] = self._heuristic(start, end)
        
        # 将起点加入开放列表
        heapq.heappush(open_list, (f_score[start], start))
        
        while open_list:
            # 取出f值最小的节点
            _, current = heapq.heappop(open_list)
            
            # 如果到达终点，构建路径并返回
            if current == end:
                path = [current]
                while current in parent:
                    current = parent[current]
                    path.append(current)
                return path[::-1]  # 反转路径
            
            # 将当前节点加入关闭列表
            closed_set.add(current)
            
            # 遍历当前节点的所有邻居
            for neighbor, distance in self.graph[current].items():
                # 如果邻居在关闭列表中，跳过
                if neighbor in closed_set:
                    continue
                
                # 计算从起点经过当前节点到邻居的距离
                tentative_g_score = g_score[current] + distance
                
                # 如果找到了更短的路径，更新g值和f值
                if tentative_g_score < g_score[neighbor]:
                    parent[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = g_score[neighbor] + self._heuristic(neighbor, end)
                    
                    # 将邻居加入开放列表
                    heapq.heappush(open_list, (f_score[neighbor], neighbor))
        
        # 如果无法找到路径，返回空列表
        return []
    
    def _heuristic(self, point1, point2):
        """
        计算两点之间的启发式距离（曼哈顿距离）
        
        Args:
            point1: 点1的ID
            point2: 点2的ID
            
        Returns:
            float: 启发式距离
        """
        x1, y1 = self.task_points[point1]['x'], self.task_points[point1]['y']
        x2, y2 = self.task_points[point2]['x'], self.task_points[point2]['y']
        
        return abs(x1 - x2) + abs(y1 - y2)
    
    def _dijkstra(self, start, end, obstacle_pool=None, current_time=None):
        """
        Dijkstra算法规划路径
        
        Args:
            start: 起点ID
            end: 终点ID
            obstacle_pool: 障碍物池
            current_time: 当前时间
            
        Returns:
            list: 路径点列表，每个元素为点ID
        """
        # TODO: 实现Dijkstra算法
        # 这里简单地调用A*算法作为占位符
        return self._astar(start, end, obstacle_pool, current_time)
    
    def _rrt(self, start, end, obstacle_pool=None, current_time=None):
        """
        快速随机树算法规划路径
        
        Args:
            start: 起点ID
            end: 终点ID
            obstacle_pool: 障碍物池
            current_time: 当前时间
            
        Returns:
            list: 路径点列表，每个元素为点ID
        """
        # TODO: 实现RRT算法
        # 这里简单地调用A*算法作为占位符
        return self._astar(start, end, obstacle_pool, current_time)
    
    def _custom_path_planning(self, start, end, obstacle_pool=None, current_time=None):
        """
        自定义算法规划路径
        
        Args:
            start: 起点ID
            end: 终点ID
            obstacle_pool: 障碍物池
            current_time: 当前时间
            
        Returns:
            list: 路径点列表，每个元素为点ID
        """
        # TODO: 实现自定义算法
        # 这里简单地调用A*算法作为占位符
        return self._astar(start, end, obstacle_pool, current_time)
    
    def check_path_conflict(self, path, obstacle_pool, current_time):
        """
        检查路径是否与障碍物冲突
        
        Args:
            path: 路径点列表
            obstacle_pool: 障碍物池
            current_time: 当前时间
            
        Returns:
            bool: 是否存在冲突
        """
        if not path or not obstacle_pool:
            return False
        
        # 检查路径上的每个通道是否与障碍物冲突
        for i in range(len(path) - 1):
            start_id = path[i]
            end_id = path[i + 1]
            
            # 获取通道的起点和终点坐标
            start_x, start_y = self.task_points[start_id]['x'], self.task_points[start_id]['y']
            end_x, end_y = self.task_points[end_id]['x'], self.task_points[end_id]['y']
            
            # 检查通道是否与障碍物冲突
            for obstacle in obstacle_pool:
                if not obstacle.is_active(current_time):
                    continue
                
                # 简单地检查通道的起点和终点是否在障碍物内部
                if self._is_point_in_obstacle((start_x, start_y), obstacle) or \
                   self._is_point_in_obstacle((end_x, end_y), obstacle):
                    return True
                
                # 检查通道是否与障碍物相交
                if self._is_line_intersect_obstacle((start_x, start_y), (end_x, end_y), obstacle):
                    return True
        
        return False
    
    def _is_point_in_obstacle(self, point, obstacle):
        """
        检查点是否在障碍物内部
        
        Args:
            point: 点坐标 (x, y)
            obstacle: 障碍物对象
            
        Returns:
            bool: 是否在障碍物内部
        """
        x, y = point
        top_left = obstacle.top_left
        bottom_right = obstacle.bottom_right
        
        return (top_left[0] <= x <= bottom_right[0] and
                top_left[1] <= y <= bottom_right[1])
    
    def _is_line_intersect_obstacle(self, p1, p2, obstacle):
        """
        检查线段是否与障碍物相交
        
        Args:
            p1: 线段起点坐标 (x, y)
            p2: 线段终点坐标 (x, y)
            obstacle: 障碍物对象
            
        Returns:
            bool: 是否相交
        """
        # 获取障碍物的四个顶点
        top_left = obstacle.top_left
        bottom_right = obstacle.bottom_right
        top_right = (bottom_right[0], top_left[1])
        bottom_left = (top_left[0], bottom_right[1])
        
        # 检查线段是否与障碍物的四条边相交
        edges = [
            (top_left, top_right),
            (top_right, bottom_right),
            (bottom_right, bottom_left),
            (bottom_left, top_left)
        ]
        
        for edge in edges:
            if self._is_lines_intersect(p1, p2, edge[0], edge[1]):
                return True
        
        return False
    
    def _is_lines_intersect(self, p1, p2, p3, p4):
        """
        检查两条线段是否相交
        
        Args:
            p1, p2: 第一条线段的端点坐标
            p3, p4: 第二条线段的端点坐标
            
        Returns:
            bool: 是否相交
        """
        # 计算方向
        d1 = self._direction(p3, p4, p1)
        d2 = self._direction(p3, p4, p2)
        d3 = self._direction(p1, p2, p3)
        d4 = self._direction(p1, p2, p4)
        
        # 如果两条线段相交，则它们的方向必须不同
        if ((d1 > 0 and d2 < 0) or (d1 < 0 and d2 > 0)) and \
           ((d3 > 0 and d4 < 0) or (d3 < 0 and d4 > 0)):
            return True
        
        # 检查共线的情况
        if d1 == 0 and self._on_segment(p3, p4, p1):
            return True
        if d2 == 0 and self._on_segment(p3, p4, p2):
            return True
        if d3 == 0 and self._on_segment(p1, p2, p3):
            return True
        if d4 == 0 and self._on_segment(p1, p2, p4):
            return True
        
        return False
    
    def _direction(self, p1, p2, p3):
        """
        计算三点的方向
        
        Args:
            p1, p2, p3: 三个点的坐标
            
        Returns:
            int: 方向值，>0表示逆时针，<0表示顺时针，=0表示共线
        """
        return (p3[1] - p1[1]) * (p2[0] - p1[0]) - (p2[1] - p1[1]) * (p3[0] - p1[0])
    
    def _on_segment(self, p1, p2, p3):
        """
        检查点p3是否在线段p1-p2上
        
        Args:
            p1, p2: 线段的端点坐标
            p3: 待检查的点坐标
            
        Returns:
            bool: 是否在线段上
        """
        return (min(p1[0], p2[0]) <= p3[0] <= max(p1[0], p2[0]) and
                min(p1[1], p2[1]) <= p3[1] <= max(p1[1], p2[1]))
    
    def calculate_path_length(self, path):
        """
        计算路径长度
        
        Args:
            path: 路径点列表
            
        Returns:
            float: 路径长度
        """
        if not path or len(path) < 2:
            return 0
        
        length = 0
        for i in range(len(path) - 1):
            start_id = path[i]
            end_id = path[i + 1]
            
            # 获取通道距离
            if (start_id, end_id) in self.channels:
                length += self.channels[(start_id, end_id)].get('distance', 1)
            elif (end_id, start_id) in self.channels:
                length += self.channels[(end_id, start_id)].get('distance', 1)
            else:
                # 如果通道不存在，使用欧氏距离
                start_x, start_y = self.task_points[start_id]['x'], self.task_points[start_id]['y']
                end_x, end_y = self.task_points[end_id]['x'], self.task_points[end_id]['y']
                length += math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)
        
        return length
    
    def update_position(self, agv, move_distance, current_time):
        """
        更新AGV位置
        
        Args:
            agv: AGV对象
            move_distance: 移动距离
            current_time: 当前时间
            
        Returns:
            str: 新位置（任务点ID）
        """
        if not agv.path or len(agv.path) < 2 or agv.path_index >= len(agv.path) - 1:
            return agv.position
        
        # 获取当前路径段
        current_point = agv.path[agv.path_index]
        next_point = agv.path[agv.path_index + 1]
        
        # 获取当前点和下一个点的坐标
        current_x, current_y = self.task_points[current_point]['x'], self.task_points[current_point]['y']
        next_x, next_y = self.task_points[next_point]['x'], self.task_points[next_point]['y']
        
        # 计算当前路径段的长度
        segment_length = math.sqrt((next_x - current_x) ** 2 + (next_y - current_y) ** 2)
        
        # 如果移动距离大于等于当前路径段的长度，直接移动到下一个点
        if move_distance >= segment_length:
            agv.position = next_point
            agv.path_index += 1
            
            # 如果还有剩余的移动距离，继续移动
            remaining_distance = move_distance - segment_length
            if remaining_distance > 0 and agv.path_index < len(agv.path) - 1:
                return self.update_position(agv, remaining_distance, current_time)
        else:
            # 计算新位置的坐标（在当前路径段上按比例移动）
            ratio = move_distance / segment_length
            new_x = current_x + ratio * (next_x - current_x)
            new_y = current_y + ratio * (next_y - current_y)
            
            # 更新AGV位置为新坐标
            agv.position = (new_x, new_y)
        
        return agv.position
